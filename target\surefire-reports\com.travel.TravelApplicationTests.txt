-------------------------------------------------------------------------------
Test set: com.travel.TravelApplicationTests
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 2.336 s <<< FAILURE! - in com.travel.TravelApplicationTests
contextLoads  Time elapsed: 0.009 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'apiModelPropertyPropertyBuilder' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'springfox.documentation.spring.web.DescriptionResolver' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'springfox.documentation.spring.web.DescriptionResolver' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}

